import React, { useState, useContext } from "react";
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
} from "@mui/material";
import {
  Analytics,
  TrendingUp,
  People,
  Message,
  Schedule,
  Star,
  Group,
  NetworkCheck,
  BarChart,
  PieChart,
} from "@mui/icons-material";
import { useQuery } from "react-query";
import { store } from "../store";
import styled from "styled-components";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs, { Dayjs } from "dayjs";
import {
  Activity<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  MessageTypeDistribution<PERSON>hart,
  ActivityTimelineChart,
  TopChannelsChart,
  InfluenceNetworkChart,
} from "./AnalyticsCharts";

const DashboardContainer = styled.div`
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
`;

const MetricCard = styled(Card)`
  height: 100%;
  display: flex;
  flex-direction: column;

  .metric-value {
    font-size: 2rem;
    font-weight: bold;
    color: #1976d2;
  }

  .metric-change {
    font-size: 0.875rem;
    color: #4caf50;
  }
`;

const ChartContainer = styled(Paper)`
  padding: 16px;
  height: 400px;
  display: flex;
  flex-direction: column;
`;

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`analytics-tabpanel-${index}`}
      aria-labelledby={`analytics-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export function AnalyticsDashboard() {
  const { state } = useContext(store);
  const [tabValue, setTabValue] = useState(0);
  const [dateRange, setDateRange] = useState({
    from: dayjs().subtract(30, "day"),
    to: dayjs(),
  });
  const [granularity, setGranularity] = useState("day");

  // Fetch global analytics
  const {
    data: globalAnalytics,
    isLoading: globalLoading,
    error: globalError,
  } = useQuery(
    ["global-analytics", dateRange, granularity],
    async () => {
      const params = new URLSearchParams({
        from: dateRange.from.toISOString(),
        to: dateRange.to.toISOString(),
        granularity,
        limit: "20",
      });

      const response = await fetch(`${state.apiBaseUrl}/analytics?${params}`);
      if (!response.ok) throw new Error("Failed to fetch analytics");
      return response.json();
    },
    { enabled: !!state.apiBaseUrl }
  );

  // Fetch social network analysis
  const {
    data: socialAnalysis,
    isLoading: socialLoading,
    error: socialError,
  } = useQuery(
    ["social-network-analysis", dateRange],
    async () => {
      const params = new URLSearchParams({
        from: dateRange.from.toISOString(),
        to: dateRange.to.toISOString(),
        limit: "50",
      });

      const response = await fetch(
        `${state.apiBaseUrl}/analytics/social-network?${params}`
      );
      if (!response.ok) throw new Error("Failed to fetch social analysis");
      return response.json();
    },
    { enabled: !!state.apiBaseUrl }
  );

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const formatPercentage = (num: number) => `${num.toFixed(1)}%`;

  if (globalLoading) {
    return (
      <DashboardContainer>
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          minHeight="400px"
        >
          <CircularProgress />
          <Typography variant="h6" sx={{ ml: 2 }}>
            Loading analytics...
          </Typography>
        </Box>
      </DashboardContainer>
    );
  }

  if (globalError) {
    return (
      <DashboardContainer>
        <Alert severity="error">
          Failed to load analytics: {(globalError as Error).message}
        </Alert>
      </DashboardContainer>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <DashboardContainer>
        <Box display="flex" alignItems="center" gap={2} mb={3}>
          <Analytics color="primary" fontSize="large" />
          <Typography variant="h4" component="h1">
            Analytics Dashboard
          </Typography>
        </Box>

        {/* Date Range Controls */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={3}>
              <DateTimePicker
                label="From"
                value={dateRange.from}
                onChange={(value) =>
                  setDateRange((prev) => ({ ...prev, from: value || dayjs() }))
                }
                slotProps={{ textField: { size: "small", fullWidth: true } }}
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <DateTimePicker
                label="To"
                value={dateRange.to}
                onChange={(value) =>
                  setDateRange((prev) => ({ ...prev, to: value || dayjs() }))
                }
                slotProps={{ textField: { size: "small", fullWidth: true } }}
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <FormControl size="small" fullWidth>
                <InputLabel>Granularity</InputLabel>
                <Select
                  value={granularity}
                  label="Granularity"
                  onChange={(e) => setGranularity(e.target.value)}
                >
                  <MenuItem value="hour">Hour</MenuItem>
                  <MenuItem value="day">Day</MenuItem>
                  <MenuItem value="week">Week</MenuItem>
                  <MenuItem value="month">Month</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={3}>
              <Button
                variant="contained"
                startIcon={<TrendingUp />}
                onClick={() => window.location.reload()}
                fullWidth
              >
                Refresh
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Key Metrics */}
        {globalAnalytics && (
          <Grid container spacing={3} mb={3}>
            <Grid item xs={12} sm={6} md={3}>
              <MetricCard>
                <CardContent>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Message color="primary" />
                    <Typography variant="h6">Total Messages</Typography>
                  </Box>
                  <Typography className="metric-value">
                    {formatNumber(globalAnalytics.totalMessages)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Across all channels
                  </Typography>
                </CardContent>
              </MetricCard>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <MetricCard>
                <CardContent>
                  <Box display="flex" alignItems="center" gap={1}>
                    <People color="primary" />
                    <Typography variant="h6">Total Users</Typography>
                  </Box>
                  <Typography className="metric-value">
                    {formatNumber(globalAnalytics.totalUsers)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Unique participants
                  </Typography>
                </CardContent>
              </MetricCard>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <MetricCard>
                <CardContent>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Group color="primary" />
                    <Typography variant="h6">Total Channels</Typography>
                  </Box>
                  <Typography className="metric-value">
                    {formatNumber(globalAnalytics.totalChannels)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active channels
                  </Typography>
                </CardContent>
              </MetricCard>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <MetricCard>
                <CardContent>
                  <Box display="flex" alignItems="center" gap={1}>
                    <TrendingUp color="primary" />
                    <Typography variant="h6">New Users/Day</Typography>
                  </Box>
                  <Typography className="metric-value">
                    {formatNumber(
                      globalAnalytics.growthMetrics?.newUsersPerDay || 0
                    )}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Average growth
                  </Typography>
                </CardContent>
              </MetricCard>
            </Grid>
          </Grid>
        )}

        {/* Tabs for different views */}
        <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Overview" icon={<BarChart />} />
            <Tab label="Top Channels" icon={<Group />} />
            <Tab label="Top Users" icon={<People />} />
            <Tab label="Social Network" icon={<NetworkCheck />} />
          </Tabs>
        </Box>

        {/* Overview Tab */}
        <TabPanel value={tabValue} index={0}>
          {globalAnalytics && (
            <Grid container spacing={3}>
              {/* Message Types Distribution */}
              <Grid item xs={12} md={6}>
                <ChartContainer>
                  <Typography variant="h6" gutterBottom>
                    Message Types Distribution
                  </Typography>
                  <List>
                    {globalAnalytics.messageDistribution?.map(
                      (type: any, index: number) => (
                        <ListItem key={index}>
                          <ListItemIcon>
                            <PieChart color="primary" />
                          </ListItemIcon>
                          <ListItemText
                            primary={type.messageType}
                            secondary={`${formatNumber(
                              type.count
                            )} messages (${formatPercentage(type.percentage)})`}
                          />
                        </ListItem>
                      )
                    )}
                  </List>
                </ChartContainer>
              </Grid>

              {/* Activity Heatmap */}
              <Grid item xs={12} md={6}>
                <ChartContainer>
                  <Typography variant="h6" gutterBottom>
                    Hourly Activity Pattern
                  </Typography>
                  <Grid container spacing={1}>
                    {globalAnalytics.activityHeatmap?.map(
                      (activity: any, index: number) => (
                        <Grid item xs={1} key={index}>
                          <Box
                            sx={{
                              height: 40,
                              backgroundColor: `rgba(25, 118, 210, ${
                                activity.messageCount /
                                Math.max(
                                  ...globalAnalytics.activityHeatmap.map(
                                    (a: any) => a.messageCount
                                  )
                                )
                              })`,
                              borderRadius: 1,
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              color: "white",
                              fontSize: "0.75rem",
                            }}
                            title={`Hour ${activity.hour}: ${formatNumber(
                              activity.messageCount
                            )} messages`}
                          >
                            {activity.hour}
                          </Box>
                        </Grid>
                      )
                    )}
                  </Grid>
                </ChartContainer>
              </Grid>
            </Grid>
          )}
        </TabPanel>

        {/* Top Channels Tab */}
        <TabPanel value={tabValue} index={1}>
          {globalAnalytics?.topChannels && (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Channel</TableCell>
                    <TableCell align="right">Messages</TableCell>
                    <TableCell align="right">Unique Users</TableCell>
                    <TableCell align="right">Avg Daily Messages</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {globalAnalytics.topChannels.map(
                    (channel: any, index: number) => (
                      <TableRow key={channel.channelId}>
                        <TableCell>
                          <Box display="flex" alignItems="center" gap={1}>
                            <Chip
                              label={index + 1}
                              size="small"
                              color="primary"
                            />
                            {channel.channelName}
                          </Box>
                        </TableCell>
                        <TableCell align="right">
                          {formatNumber(channel.messageCount)}
                        </TableCell>
                        <TableCell align="right">
                          {formatNumber(channel.uniqueUsers)}
                        </TableCell>
                        <TableCell align="right">
                          {formatNumber(channel.avgDailyMessages)}
                        </TableCell>
                      </TableRow>
                    )
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </TabPanel>

        {/* Top Users Tab */}
        <TabPanel value={tabValue} index={2}>
          {globalAnalytics?.topUsers && (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>User</TableCell>
                    <TableCell align="right">Messages</TableCell>
                    <TableCell align="right">Channels Active</TableCell>
                    <TableCell align="right">Avg Message Length</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {globalAnalytics.topUsers.map((user: any, index: number) => (
                    <TableRow key={user.userId}>
                      <TableCell>
                        <Box display="flex" alignItems="center" gap={1}>
                          <Chip
                            label={index + 1}
                            size="small"
                            color="secondary"
                          />
                          {user.username}
                        </Box>
                      </TableCell>
                      <TableCell align="right">
                        {formatNumber(user.messageCount)}
                      </TableCell>
                      <TableCell align="right">{user.channelsActive}</TableCell>
                      <TableCell align="right">
                        {user.avgMessageLength.toFixed(1)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </TabPanel>

        {/* Social Network Tab */}
        <TabPanel value={tabValue} index={3}>
          {socialLoading ? (
            <Box display="flex" justifyContent="center" p={4}>
              <CircularProgress />
              <Typography sx={{ ml: 2 }}>
                Loading social network analysis...
              </Typography>
            </Box>
          ) : socialError ? (
            <Alert severity="error">
              Failed to load social network analysis:{" "}
              {(socialError as Error).message}
            </Alert>
          ) : socialAnalysis ? (
            <Grid container spacing={3}>
              {/* Influence Metrics */}
              <Grid item xs={12}>
                <Paper sx={{ p: 2 }}>
                  <Typography variant="h6" gutterBottom>
                    Top Influencers
                  </Typography>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>User</TableCell>
                          <TableCell align="right">Influence Score</TableCell>
                          <TableCell align="right">Reach</TableCell>
                          <TableCell align="right">Engagement Rate</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {socialAnalysis.influenceMetrics
                          ?.slice(0, 10)
                          .map((metric: any, index: number) => (
                            <TableRow key={metric.userId}>
                              <TableCell>
                                <Box display="flex" alignItems="center" gap={1}>
                                  <Avatar sx={{ width: 24, height: 24 }}>
                                    <Star />
                                  </Avatar>
                                  {metric.username}
                                </Box>
                              </TableCell>
                              <TableCell align="right">
                                {metric.influenceScore.toFixed(2)}
                              </TableCell>
                              <TableCell align="right">
                                {formatNumber(metric.reach)}
                              </TableCell>
                              <TableCell align="right">
                                {formatPercentage(metric.engagementRate * 100)}
                              </TableCell>
                            </TableRow>
                          ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Paper>
              </Grid>
            </Grid>
          ) : null}
        </TabPanel>
      </DashboardContainer>
    </LocalizationProvider>
  );
}
