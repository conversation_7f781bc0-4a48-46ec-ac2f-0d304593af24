#!/usr/bin/env python3
"""
Comprehensive test script for the new global search functionality and analytics features.
Tests both simple global search, advanced global search with various filters, and analytics endpoints.
"""

import requests
import json
import time
import argparse
from datetime import datetime, timedelta
from urllib.parse import urlencode

class GlobalSearchTester:
    def __init__(self, base_url="http://localhost:3000"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name, success, details="", response_time=None):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = {
            "test": test_name,
            "success": success,
            "details": details,
            "response_time": response_time,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        time_info = f" ({response_time:.2f}s)" if response_time else ""
        print(f"{status} {test_name}{time_info}")
        if details:
            print(f"    {details}")
    
    def test_endpoint(self, url, test_name, expected_status=200):
        """Test a single endpoint"""
        try:
            start_time = time.time()
            response = self.session.get(url, timeout=30)
            response_time = time.time() - start_time
            
            success = response.status_code == expected_status
            
            if success and response.status_code == 200:
                try:
                    data = response.json()
                    message_count = len(data.get('messages', []))
                    details = f"Status: {response.status_code}, Messages: {message_count}"
                except:
                    details = f"Status: {response.status_code}, Response length: {len(response.text)}"
            else:
                details = f"Status: {response.status_code}, Expected: {expected_status}"
                
            self.log_test(test_name, success, details, response_time)
            return response
            
        except requests.exceptions.RequestException as e:
            self.log_test(test_name, False, f"Request failed: {str(e)}")
            return None
    
    def test_simple_global_search(self):
        """Test simple global search endpoint"""
        print("\n🔍 Testing Simple Global Search")
        print("-" * 50)
        
        # Test basic global search
        self.test_endpoint(
            f"{self.base_url}/search?q=hello&jsonBasic=1",
            "Simple Global Search - Basic Query"
        )
        
        # Test global search with empty query
        self.test_endpoint(
            f"{self.base_url}/search?q=&jsonBasic=1",
            "Simple Global Search - Empty Query"
        )
        
        # Test global search with special characters
        self.test_endpoint(
            f"{self.base_url}/search?q=test%20message&jsonBasic=1",
            "Simple Global Search - URL Encoded Query"
        )
        
        # Test global search with reverse order
        self.test_endpoint(
            f"{self.base_url}/search?q=hello&jsonBasic=1&reverse=1",
            "Simple Global Search - Reverse Order"
        )
    
    def test_advanced_global_search(self):
        """Test advanced global search endpoint"""
        print("\n🔍 Testing Advanced Global Search")
        print("-" * 50)
        
        # Test basic advanced search
        self.test_endpoint(
            f"{self.base_url}/advanced-search?q=hello&jsonBasic=1",
            "Advanced Global Search - Basic Query"
        )
        
        # Test with channel filtering
        self.test_endpoint(
            f"{self.base_url}/advanced-search?q=hello&channels=forsen,xqc&jsonBasic=1",
            "Advanced Global Search - Channel Filter"
        )
        
        # Test with user filtering
        self.test_endpoint(
            f"{self.base_url}/advanced-search?q=hello&users=testuser&jsonBasic=1",
            "Advanced Global Search - User Filter"
        )
        
        # Test with exclude users
        self.test_endpoint(
            f"{self.base_url}/advanced-search?q=hello&excludeUsers=bot&jsonBasic=1",
            "Advanced Global Search - Exclude Users"
        )
        
        # Test with message type filtering
        self.test_endpoint(
            f"{self.base_url}/advanced-search?q=hello&messageTypes=privmsg&jsonBasic=1",
            "Advanced Global Search - Message Type Filter"
        )
        
        # Test with subscriber filter
        self.test_endpoint(
            f"{self.base_url}/advanced-search?q=hello&subscribersOnly=1&jsonBasic=1",
            "Advanced Global Search - Subscribers Only"
        )
        
        # Test with VIP filter
        self.test_endpoint(
            f"{self.base_url}/advanced-search?q=hello&vipsOnly=1&jsonBasic=1",
            "Advanced Global Search - VIPs Only"
        )
        
        # Test with moderator filter
        self.test_endpoint(
            f"{self.base_url}/advanced-search?q=hello&moderatorsOnly=1&jsonBasic=1",
            "Advanced Global Search - Moderators Only"
        )
        
        # Test with regex
        self.test_endpoint(
            f"{self.base_url}/advanced-search?q=hello.*world&regex=1&jsonBasic=1",
            "Advanced Global Search - Regex Pattern"
        )
        
        # Test case sensitive search
        self.test_endpoint(
            f"{self.base_url}/advanced-search?q=Hello&caseSensitive=1&jsonBasic=1",
            "Advanced Global Search - Case Sensitive"
        )
        
        # Test with date range (last 7 days)
        to_date = datetime.now()
        from_date = to_date - timedelta(days=7)
        self.test_endpoint(
            f"{self.base_url}/advanced-search?q=hello&from={from_date.isoformat()}&to={to_date.isoformat()}&jsonBasic=1",
            "Advanced Global Search - Date Range"
        )
        
        # Test with message length filters
        self.test_endpoint(
            f"{self.base_url}/advanced-search?q=hello&minLength=10&maxLength=100&jsonBasic=1",
            "Advanced Global Search - Message Length Filter"
        )
        
        # Test search in display names
        self.test_endpoint(
            f"{self.base_url}/advanced-search?q=test&searchDisplayNames=1&jsonBasic=1",
            "Advanced Global Search - Display Names"
        )
        
        # Test with emotes filter
        self.test_endpoint(
            f"{self.base_url}/advanced-search?q=hello&hasEmotes=1&jsonBasic=1",
            "Advanced Global Search - Has Emotes"
        )
        
        # Test first messages only
        self.test_endpoint(
            f"{self.base_url}/advanced-search?q=hello&firstMessagesOnly=1&jsonBasic=1",
            "Advanced Global Search - First Messages Only"
        )
    
    def test_performance(self):
        """Test performance of global search"""
        print("\n⚡ Testing Performance")
        print("-" * 50)
        
        # Test with common search terms
        common_terms = ["the", "and", "hello", "test", "lol"]
        
        for term in common_terms:
            self.test_endpoint(
                f"{self.base_url}/search?q={term}&jsonBasic=1",
                f"Performance Test - '{term}'"
            )
    
    def test_edge_cases(self):
        """Test edge cases and error conditions"""
        print("\n🧪 Testing Edge Cases")
        print("-" * 50)
        
        # Test very long query
        long_query = "a" * 1000
        self.test_endpoint(
            f"{self.base_url}/search?q={long_query}&jsonBasic=1",
            "Edge Case - Very Long Query"
        )
        
        # Test special characters
        special_chars = "!@#$%^&*()[]{}|\\:;\"'<>,.?/~`"
        self.test_endpoint(
            f"{self.base_url}/search?q={requests.utils.quote(special_chars)}&jsonBasic=1",
            "Edge Case - Special Characters"
        )
        
        # Test invalid regex
        self.test_endpoint(
            f"{self.base_url}/advanced-search?q=[invalid&regex=1&jsonBasic=1",
            "Edge Case - Invalid Regex"
        )
        
        # Test invalid date format
        self.test_endpoint(
            f"{self.base_url}/advanced-search?q=hello&from=invalid-date&jsonBasic=1",
            "Edge Case - Invalid Date Format"
        )
    
    def test_comparison_with_channel_search(self):
        """Compare global search with channel-specific search"""
        print("\n🔄 Testing Comparison with Channel Search")
        print("-" * 50)
        
        # Test same query on global vs channel-specific
        query = "hello"
        
        # Global search
        global_response = self.test_endpoint(
            f"{self.base_url}/search?q={query}&jsonBasic=1",
            "Comparison - Global Search"
        )
        
        # Channel-specific search (assuming 'forsen' channel exists)
        channel_response = self.test_endpoint(
            f"{self.base_url}/channel/forsen/search?q={query}&jsonBasic=1",
            "Comparison - Channel Search (forsen)"
        )
        
        # Compare results
        if global_response and channel_response:
            try:
                global_data = global_response.json()
                channel_data = channel_response.json()
                
                global_count = len(global_data.get('messages', []))
                channel_count = len(channel_data.get('messages', []))
                
                success = global_count >= channel_count
                details = f"Global: {global_count} messages, Channel: {channel_count} messages"
                self.log_test("Comparison - Result Count Logic", success, details)
                
            except Exception as e:
                self.log_test("Comparison - Result Count Logic", False, f"Error: {str(e)}")
    
    def print_summary(self):
        """Print test summary"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test']}: {result['details']}")
        
        # Performance summary
        response_times = [r['response_time'] for r in self.test_results if r['response_time']]
        if response_times:
            avg_time = sum(response_times) / len(response_times)
            max_time = max(response_times)
            min_time = min(response_times)
            
            print(f"\n⚡ Performance Summary:")
            print(f"  Average Response Time: {avg_time:.2f}s")
            print(f"  Fastest Response: {min_time:.2f}s")
            print(f"  Slowest Response: {max_time:.2f}s")
    
    def test_analytics_endpoints(self):
        """Test analytics and data analysis endpoints"""
        print("\n📊 Testing Analytics Endpoints")
        print("-" * 50)

        # Test global analytics
        self.test_endpoint(
            f"{self.base_url}/analytics",
            "Analytics - Global Analytics"
        )

        # Test global analytics with date range
        to_date = datetime.now()
        from_date = to_date - timedelta(days=7)
        self.test_endpoint(
            f"{self.base_url}/analytics?from={from_date.isoformat()}&to={to_date.isoformat()}",
            "Analytics - Global Analytics with Date Range"
        )

        # Test global analytics with granularity
        self.test_endpoint(
            f"{self.base_url}/analytics?granularity=hour&limit=24",
            "Analytics - Global Analytics Hourly"
        )

        # Test social network analysis
        self.test_endpoint(
            f"{self.base_url}/analytics/social-network",
            "Analytics - Social Network Analysis"
        )

        # Test social network analysis with parameters
        self.test_endpoint(
            f"{self.base_url}/analytics/social-network?from={from_date.isoformat()}&limit=20",
            "Analytics - Social Network Analysis with Parameters"
        )

        # Test channel analytics (assuming 'forsen' channel exists)
        self.test_endpoint(
            f"{self.base_url}/channel/forsen/analytics",
            "Analytics - Channel Analytics (forsen)"
        )

        # Test channel analytics with parameters
        self.test_endpoint(
            f"{self.base_url}/channel/forsen/analytics?granularity=day&limit=30",
            "Analytics - Channel Analytics with Parameters"
        )

    def test_data_analysis_features(self):
        """Test advanced data analysis features"""
        print("\n🔬 Testing Data Analysis Features")
        print("-" * 50)

        # Test analytics with different granularities
        granularities = ["hour", "day", "week", "month"]
        for granularity in granularities:
            self.test_endpoint(
                f"{self.base_url}/analytics?granularity={granularity}&limit=10",
                f"Data Analysis - {granularity.title()} Granularity"
            )

        # Test analytics with channel filtering
        self.test_endpoint(
            f"{self.base_url}/analytics?channels=forsen,xqc&limit=5",
            "Data Analysis - Multi-Channel Analytics"
        )

        # Test analytics with user filtering
        self.test_endpoint(
            f"{self.base_url}/analytics?users=testuser,anotheruser&limit=5",
            "Data Analysis - User-Specific Analytics"
        )

        # Test social network analysis with different parameters
        self.test_endpoint(
            f"{self.base_url}/analytics/social-network?limit=100",
            "Data Analysis - Extended Social Network Analysis"
        )

    def test_visualization_data(self):
        """Test endpoints that provide data for visualizations"""
        print("\n📈 Testing Visualization Data Endpoints")
        print("-" * 50)

        # Test data that would be used for charts and graphs
        response = self.test_endpoint(
            f"{self.base_url}/analytics",
            "Visualization - Global Analytics Data Structure"
        )

        if response and response.status_code == 200:
            try:
                data = response.json()

                # Check for expected data structure for visualizations
                expected_fields = [
                    'totalChannels', 'totalMessages', 'totalUsers',
                    'topChannels', 'topUsers', 'messageDistribution',
                    'activityHeatmap', 'growthMetrics'
                ]

                missing_fields = [field for field in expected_fields if field not in data]

                if missing_fields:
                    self.log_test(
                        "Visualization - Data Structure Validation",
                        False,
                        f"Missing fields: {missing_fields}"
                    )
                else:
                    self.log_test(
                        "Visualization - Data Structure Validation",
                        True,
                        f"All expected fields present: {len(expected_fields)} fields"
                    )

                # Test activity heatmap data
                if 'activityHeatmap' in data and data['activityHeatmap']:
                    heatmap_hours = len(data['activityHeatmap'])
                    self.log_test(
                        "Visualization - Activity Heatmap Data",
                        heatmap_hours > 0,
                        f"Heatmap contains {heatmap_hours} hour entries"
                    )

                # Test top channels data
                if 'topChannels' in data and data['topChannels']:
                    top_channels_count = len(data['topChannels'])
                    self.log_test(
                        "Visualization - Top Channels Data",
                        top_channels_count > 0,
                        f"Top channels list contains {top_channels_count} entries"
                    )

            except Exception as e:
                self.log_test(
                    "Visualization - Data Structure Validation",
                    False,
                    f"Error parsing response: {str(e)}"
                )

    def run_all_tests(self):
        """Run all test suites"""
        print(f"🧪 Starting Comprehensive Global Search & Analytics Tests")
        print(f"📡 Base URL: {self.base_url}")
        print("=" * 60)

        # Run test suites
        self.test_simple_global_search()
        self.test_advanced_global_search()
        self.test_analytics_endpoints()
        self.test_data_analysis_features()
        self.test_visualization_data()
        self.test_performance()
        self.test_edge_cases()
        self.test_comparison_with_channel_search()

        # Print summary
        self.print_summary()

def main():
    parser = argparse.ArgumentParser(description='Test global search functionality')
    parser.add_argument('--url', default='http://localhost:3000', 
                       help='Base URL of the API (default: http://localhost:3000)')
    
    args = parser.parse_args()
    
    tester = GlobalSearchTester(args.url)
    tester.run_all_tests()

if __name__ == "__main__":
    main()
