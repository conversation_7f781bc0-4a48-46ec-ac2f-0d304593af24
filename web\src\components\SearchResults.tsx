import React, { useState, useContext } from "react";
import {
  Box,
  Typography,
  Paper,
  Chip,
  Button,
  CircularProgress,
  Alert,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
} from "@mui/material";
import {
  Download,
  Share,
  FilterList,
  Sort,
  Visibility,
  VisibilityOff,
} from "@mui/icons-material";
import styled from "styled-components";
import { LogMessage } from "../types/log";
import { LogLine } from "./LogLine";
import { store } from "../store";

const SearchResultsContainer = styled(Paper)`
  margin: 1rem 0;
  padding: 1rem;
`;

const ResultsHeader = styled(Box)`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 1rem;
`;

const ResultsStats = styled(Box)`
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
`;

const ResultsList = styled(Box)`
  max-height: 600px;
  overflow-y: auto;
  border: 1px solid #333;
  border-radius: 4px;
  background: #1a1a1a;
`;

const ResultItem = styled(Box)`
  padding: 0.5rem;
  border-bottom: 1px solid #333;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: #2a2a2a;
  }
`;

const HighlightedText = styled.span`
  background: #ffeb3b;
  color: #000;
  padding: 0 2px;
  border-radius: 2px;
`;

interface SearchResultsProps {
  results: LogMessage[] | undefined;
  isLoading: boolean;
  error: any;
  searchQuery?: string;
  totalResults?: number;
  onExport?: () => void;
  onShare?: () => void;
  onClearSearch?: () => void;
  isGlobalSearch?: boolean;
}

const ITEMS_PER_PAGE = 50;

export function SearchResults({
  results,
  isLoading,
  error,
  searchQuery,
  totalResults,
  onExport,
  onShare,
  onClearSearch,
  isGlobalSearch = false,
}: SearchResultsProps) {
  const { state } = useContext(store);
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState<"timestamp" | "relevance">("timestamp");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [showContext, setShowContext] = useState(false);

  if (isLoading) {
    return (
      <SearchResultsContainer>
        <Box display="flex" justifyContent="center" alignItems="center" p={4}>
          <CircularProgress />
          <Typography variant="body1" sx={{ ml: 2 }}>
            Searching...
          </Typography>
        </Box>
      </SearchResultsContainer>
    );
  }

  if (error) {
    return (
      <SearchResultsContainer>
        <Alert severity="error">
          Search failed: {error.message || "Unknown error"}
        </Alert>
      </SearchResultsContainer>
    );
  }

  if (!results || results.length === 0) {
    return (
      <SearchResultsContainer>
        <Alert severity="info">No results found for "{searchQuery}"</Alert>
      </SearchResultsContainer>
    );
  }

  // Sort results
  const sortedResults = [...results].sort((a, b) => {
    if (sortBy === "timestamp") {
      const timeA = new Date(a.timestamp).getTime();
      const timeB = new Date(b.timestamp).getTime();
      return sortOrder === "asc" ? timeA - timeB : timeB - timeA;
    }
    // For relevance, we could implement a scoring system
    return 0;
  });

  // Paginate results
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const paginatedResults = sortedResults.slice(startIndex, endIndex);
  const totalPages = Math.ceil(sortedResults.length / ITEMS_PER_PAGE);

  const highlightText = (text: string, query: string) => {
    if (!query) return text;

    const regex = new RegExp(
      `(${query.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`,
      "gi"
    );
    const parts = text.split(regex);

    return parts.map((part, index) =>
      regex.test(part) ? (
        <HighlightedText key={index}>{part}</HighlightedText>
      ) : (
        part
      )
    );
  };

  const handleExport = () => {
    if (onExport) {
      onExport();
    } else {
      // Default export as JSON
      const dataStr = JSON.stringify(results, null, 2);
      const dataBlob = new Blob([dataStr], { type: "application/json" });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `search-results-${
        new Date().toISOString().split("T")[0]
      }.json`;
      link.click();
      URL.revokeObjectURL(url);
    }
  };

  return (
    <SearchResultsContainer>
      <ResultsHeader>
        <ResultsStats>
          <Typography variant="h6">Search Results</Typography>
          <Chip
            label={`${results.length} results`}
            color="primary"
            variant="outlined"
          />
          {totalResults && totalResults > results.length && (
            <Chip
              label={`${totalResults} total`}
              color="secondary"
              variant="outlined"
            />
          )}
        </ResultsStats>

        <Box display="flex" alignItems="center" gap={1}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Sort by</InputLabel>
            <Select
              value={sortBy}
              label="Sort by"
              onChange={(e) =>
                setSortBy(e.target.value as "timestamp" | "relevance")
              }
            >
              <MenuItem value="timestamp">Time</MenuItem>
              <MenuItem value="relevance">Relevance</MenuItem>
            </Select>
          </FormControl>

          <IconButton
            onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
            title={`Sort ${sortOrder === "asc" ? "descending" : "ascending"}`}
          >
            <Sort />
          </IconButton>

          <Tooltip title={showContext ? "Hide context" : "Show context"}>
            <IconButton
              onClick={() => setShowContext(!showContext)}
              color={showContext ? "primary" : "default"}
            >
              {showContext ? <VisibilityOff /> : <Visibility />}
            </IconButton>
          </Tooltip>

          <Button startIcon={<Download />} onClick={handleExport} size="small">
            Export
          </Button>

          {onClearSearch && (
            <Button onClick={onClearSearch} size="small" color="secondary">
              Clear Search
            </Button>
          )}

          {onShare && (
            <Button startIcon={<Share />} onClick={onShare} size="small">
              Share
            </Button>
          )}
        </Box>
      </ResultsHeader>

      <ResultsList>
        {paginatedResults.map((message, index) => (
          <ResultItem key={message.id || `${startIndex + index}`}>
            <LogLine message={message} />
            {showContext && (
              <Box mt={1} pl={2} sx={{ opacity: 0.7 }}>
                <Typography variant="caption">
                  Channel: {state.currentChannel} | Time:{" "}
                  {new Date(message.timestamp).toLocaleString()}
                </Typography>
              </Box>
            )}
          </ResultItem>
        ))}
      </ResultsList>

      {totalPages > 1 && (
        <Box display="flex" justifyContent="center" mt={2}>
          <Pagination
            count={totalPages}
            page={currentPage}
            onChange={(_, page) => setCurrentPage(page)}
            color="primary"
            showFirstButton
            showLastButton
          />
        </Box>
      )}
    </SearchResultsContainer>
  );
}
