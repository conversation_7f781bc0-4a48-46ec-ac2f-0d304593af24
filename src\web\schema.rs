use super::responders::logs::{JsonResponseType, LogsResponseType};
use chrono::{DateTime, Utc};
use schemars::JsonSchema;
use serde::{Deserialize, Deserializer, Serialize};
use std::fmt::Display;
use strum::Display;

#[derive(Serialize, JsonSchema)]
pub struct ChannelsList {
    pub channels: Vec<Channel>,
}

#[derive(Serialize, JsonSchema)]
pub struct Channel {
    pub name: String,
    #[serde(rename = "userID")]
    pub user_id: String,
}

#[derive(Debug, Deserialize, JsonSchema, Display)]
pub enum ChannelIdType {
    #[serde(rename = "channel")]
    #[strum(serialize = "channel")]
    Name,
    #[serde(rename = "channelid")]
    #[strum(serialize = "channelid")]
    Id,
}

#[derive(Debug, Deserialize, JsonSchema, Display)]
pub enum UserIdType {
    #[serde(rename = "user")]
    #[strum(serialize = "user")]
    Name,
    #[serde(rename = "userid")]
    #[strum(serialize = "userid")]
    Id,
}

#[derive(Deserialize, JsonSchema)]
pub struct UserLogsDatePath {
    pub year: String,
    pub month: String,
}

#[derive(Deserialize, JsonSchema)]
pub struct ChannelLogsByDatePath {
    #[serde(flatten)]
    pub channel_info: LogsPathChannel,
    #[serde(flatten)]
    pub date: LogsPathDate,
}

#[derive(Deserialize, JsonSchema)]
pub struct LogsPathDate {
    pub year: String,
    pub month: String,
    pub day: String,
}

#[derive(Deserialize, JsonSchema)]
pub struct LogsPathChannel {
    pub channel_id_type: ChannelIdType,
    pub channel: String,
}

#[derive(Deserialize, Debug, JsonSchema, Clone, Copy)]
#[serde(rename_all = "camelCase")]
pub struct LogsParams {
    #[serde(default, deserialize_with = "deserialize_bool_param")]
    pub json: bool,
    #[serde(default, deserialize_with = "deserialize_bool_param")]
    pub json_basic: bool,
    #[serde(default, deserialize_with = "deserialize_bool_param")]
    pub raw: bool,
    #[serde(default, deserialize_with = "deserialize_bool_param")]
    pub reverse: bool,
    #[serde(default, deserialize_with = "deserialize_bool_param")]
    pub ndjson: bool,
    pub limit: Option<u64>,
    pub offset: Option<u64>,
}

impl LogsParams {
    pub fn response_type(&self) -> LogsResponseType {
        if self.raw {
            LogsResponseType::Raw
        } else if self.json_basic {
            LogsResponseType::Json(JsonResponseType::Basic)
        } else if self.json {
            LogsResponseType::Json(JsonResponseType::Full)
        } else if self.ndjson {
            LogsResponseType::NdJson
        } else {
            LogsResponseType::Text
        }
    }
}

fn deserialize_bool_param<'de, D>(deserializer: D) -> Result<bool, D::Error>
where
    D: Deserializer<'de>,
{
    Ok(Option::<&str>::deserialize(deserializer)?.is_some())
}

#[derive(Deserialize, Debug, JsonSchema)]
pub struct SearchParams {
    pub q: String,
}

#[derive(Deserialize, Debug, JsonSchema, Clone)]
#[serde(rename_all = "camelCase")]
pub struct AdvancedSearchParams {
    /// Search query text
    pub q: Option<String>,
    /// Use regex pattern matching
    #[serde(default, deserialize_with = "deserialize_bool_param")]
    pub regex: bool,
    /// Case sensitive search
    #[serde(default, deserialize_with = "deserialize_bool_param")]
    pub case_sensitive: bool,
    /// Search in specific channels (comma-separated channel names or channel IDs)
    pub channels: Option<String>,
    /// Search in specific users (comma-separated usernames or user IDs)
    pub users: Option<String>,
    /// Exclude specific users (comma-separated usernames or user IDs)
    pub exclude_users: Option<String>,
    /// Filter by message types (comma-separated: privmsg,notice,clearchat,etc)
    pub message_types: Option<String>,
    /// Filter by user badges (comma-separated: subscriber,vip,moderator,etc)
    pub badges: Option<String>,
    /// Search start date (RFC 3339 format)
    #[schemars(with = "String")]
    pub from: Option<chrono::DateTime<chrono::Utc>>,
    /// Search end date (RFC 3339 format)
    #[schemars(with = "String")]
    pub to: Option<chrono::DateTime<chrono::Utc>>,
    /// Minimum message length
    pub min_length: Option<u32>,
    /// Maximum message length
    pub max_length: Option<u32>,
    /// Search only first messages from users
    #[serde(default, deserialize_with = "deserialize_bool_param")]
    pub first_messages_only: bool,
    /// Search only messages from subscribers
    #[serde(default, deserialize_with = "deserialize_bool_param")]
    pub subscribers_only: bool,
    /// Search only messages from VIPs
    #[serde(default, deserialize_with = "deserialize_bool_param")]
    pub vips_only: bool,
    /// Search only messages from moderators
    #[serde(default, deserialize_with = "deserialize_bool_param")]
    pub moderators_only: bool,
    /// Include messages with emotes only
    #[serde(default, deserialize_with = "deserialize_bool_param")]
    pub has_emotes: bool,
    /// Search in display names instead of usernames
    #[serde(default, deserialize_with = "deserialize_bool_param")]
    pub search_display_names: bool,
}

#[derive(Serialize, JsonSchema)]
#[serde(rename_all = "camelCase")]
pub struct AvailableLogs {
    pub available_logs: Vec<AvailableLogDate>,
}

#[derive(Serialize, Deserialize, JsonSchema)]
#[serde(rename_all = "camelCase")]
pub struct AnalyticsParams {
    /// Start date for analysis (RFC 3339 format)
    #[schemars(with = "String")]
    pub from: Option<chrono::DateTime<chrono::Utc>>,
    /// End date for analysis (RFC 3339 format)
    #[schemars(with = "String")]
    pub to: Option<chrono::DateTime<chrono::Utc>>,
    /// Specific channels to analyze (comma-separated)
    pub channels: Option<String>,
    /// Specific users to analyze (comma-separated)
    pub users: Option<String>,
    /// Time granularity: hour, day, week, month
    pub granularity: Option<String>,
    /// Maximum number of results to return
    pub limit: Option<u32>,
}

#[derive(Serialize, JsonSchema)]
#[serde(rename_all = "camelCase")]
pub struct ChannelAnalytics {
    pub channel_id: String,
    pub channel_name: String,
    pub total_messages: u64,
    pub unique_users: u64,
    pub avg_messages_per_day: f64,
    pub peak_activity_hour: u8,
    pub top_users: Vec<UserActivity>,
    pub message_types: Vec<MessageTypeCount>,
    pub activity_timeline: Vec<ActivityPoint>,
}

#[derive(Serialize, JsonSchema)]
#[serde(rename_all = "camelCase")]
pub struct UserActivity {
    pub user_id: String,
    pub username: String,
    pub message_count: u64,
    #[schemars(with = "String")]
    pub first_seen: chrono::DateTime<chrono::Utc>,
    #[schemars(with = "String")]
    pub last_seen: chrono::DateTime<chrono::Utc>,
    pub avg_message_length: f64,
    pub is_subscriber: bool,
    pub is_vip: bool,
    pub is_moderator: bool,
}

#[derive(Serialize, JsonSchema)]
#[serde(rename_all = "camelCase")]
pub struct MessageTypeCount {
    pub message_type: String,
    pub count: u64,
    pub percentage: f64,
}

#[derive(Serialize, JsonSchema)]
#[serde(rename_all = "camelCase")]
pub struct ActivityPoint {
    #[schemars(with = "String")]
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub message_count: u64,
    pub unique_users: u64,
}

#[derive(Serialize, JsonSchema)]
#[serde(rename_all = "camelCase")]
pub struct GlobalAnalytics {
    pub total_channels: u64,
    pub total_messages: u64,
    pub total_users: u64,
    pub date_range: DateRange,
    pub top_channels: Vec<ChannelSummary>,
    pub top_users: Vec<UserSummary>,
    pub message_distribution: Vec<MessageTypeCount>,
    pub activity_heatmap: Vec<HourlyActivity>,
    pub growth_metrics: GrowthMetrics,
}

#[derive(Serialize, JsonSchema)]
#[serde(rename_all = "camelCase")]
pub struct DateRange {
    #[schemars(with = "String")]
    pub start: chrono::DateTime<chrono::Utc>,
    #[schemars(with = "String")]
    pub end: chrono::DateTime<chrono::Utc>,
}

#[derive(Serialize, JsonSchema)]
#[serde(rename_all = "camelCase")]
pub struct ChannelSummary {
    pub channel_id: String,
    pub channel_name: String,
    pub message_count: u64,
    pub unique_users: u64,
    pub avg_daily_messages: f64,
}

#[derive(Serialize, JsonSchema)]
#[serde(rename_all = "camelCase")]
pub struct UserSummary {
    pub user_id: String,
    pub username: String,
    pub message_count: u64,
    pub channels_active: u64,
    pub avg_message_length: f64,
}

#[derive(Serialize, JsonSchema)]
#[serde(rename_all = "camelCase")]
pub struct HourlyActivity {
    pub hour: u8,
    pub message_count: u64,
    pub unique_users: u64,
    pub avg_message_length: f64,
}

#[derive(Serialize, JsonSchema)]
#[serde(rename_all = "camelCase")]
pub struct GrowthMetrics {
    pub daily_growth_rate: f64,
    pub weekly_growth_rate: f64,
    pub monthly_growth_rate: f64,
    pub new_users_per_day: f64,
    pub retention_rate: f64,
}

#[derive(Serialize, JsonSchema)]
#[serde(rename_all = "camelCase")]
pub struct SocialNetworkAnalysis {
    pub user_interactions: Vec<UserInteraction>,
    pub community_clusters: Vec<CommunityCluster>,
    pub influence_metrics: Vec<InfluenceMetric>,
    pub conversation_threads: Vec<ConversationThread>,
}

#[derive(Serialize, JsonSchema)]
#[serde(rename_all = "camelCase")]
pub struct UserInteraction {
    pub user_a: String,
    pub user_b: String,
    pub interaction_count: u64,
    pub interaction_strength: f64,
    pub channels: Vec<String>,
}

#[derive(Serialize, JsonSchema)]
#[serde(rename_all = "camelCase")]
pub struct CommunityCluster {
    pub cluster_id: u32,
    pub users: Vec<String>,
    pub cohesion_score: f64,
    pub primary_channels: Vec<String>,
    pub topics: Vec<String>,
}

#[derive(Serialize, JsonSchema)]
#[serde(rename_all = "camelCase")]
pub struct InfluenceMetric {
    pub user_id: String,
    pub username: String,
    pub influence_score: f64,
    pub reach: u64,
    pub engagement_rate: f64,
    pub response_rate: f64,
}

#[derive(Serialize, JsonSchema)]
#[serde(rename_all = "camelCase")]
pub struct ConversationThread {
    pub thread_id: String,
    pub participants: Vec<String>,
    pub message_count: u64,
    pub duration_minutes: u64,
    pub topic_keywords: Vec<String>,
    pub sentiment_score: f64,
}

#[derive(Serialize, JsonSchema)]
pub struct AvailableLogDate {
    pub year: String,
    pub month: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub day: Option<String>,
}

impl Display for AvailableLogDate {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}/{}", self.year, self.month)?;

        if let Some(day) = &self.day {
            write!(f, "/{day}")?;
        }

        Ok(())
    }
}

#[derive(Deserialize, JsonSchema)]
pub struct AvailableLogsParams {
    #[serde(flatten)]
    pub channel: ChannelParam,
    #[serde(flatten)]
    pub user: Option<UserParam>,
}

#[derive(Deserialize, JsonSchema)]
#[serde(rename_all = "lowercase")]
pub enum UserParam {
    User(String),
    UserId(String),
}

#[derive(Deserialize, JsonSchema)]
#[serde(rename_all = "lowercase")]
pub enum ChannelParam {
    Channel(String),
    ChannelId(String),
}

#[derive(Deserialize, JsonSchema)]
pub struct UserLogPathParams {
    pub channel_id_type: ChannelIdType,
    pub channel: String,
    pub user_id_type: UserIdType,
    pub user: String,
}

#[derive(Serialize, JsonSchema)]
#[serde(rename_all = "camelCase")]
pub struct ChannelLogsStats {
    pub message_count: u64,
    pub top_chatters: Vec<UserLogsStats>,
}

#[derive(Serialize, JsonSchema)]
#[serde(rename_all = "camelCase")]
pub struct UserLogsStats {
    pub user_id: String,
    pub message_count: u64,
}

#[derive(Deserialize, JsonSchema)]
pub struct UserNameHistoryParam {
    pub user_id: String,
}

#[derive(Serialize, JsonSchema)]
pub struct PreviousName {
    pub user_login: String,
    #[schemars(with = "String")]
    pub last_timestamp: DateTime<Utc>,
    #[schemars(with = "String")]
    pub first_timestamp: DateTime<Utc>,
}
