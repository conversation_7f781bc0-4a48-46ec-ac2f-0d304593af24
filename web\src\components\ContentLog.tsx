import { Input<PERSON>dorn<PERSON>, TextField, IconButton, Box } from "@mui/material";
import { Search, FilterList } from "@mui/icons-material";
import React, {
  useContext,
  useState,
  CSSProperties,
  useRef,
  useEffect,
} from "react";
import styled from "styled-components";
import { useLog } from "../hooks/useLog";
import { useAdvancedSearch } from "../hooks/useAdvancedSearch";
import { store } from "../store";
import { LogLine } from "./LogLine";
import { AdvancedSearch } from "./AdvancedSearch";
import { SearchResults } from "./SearchResults";
import { FixedSizeList as List } from "react-window";

const ContentLogContainer = styled.ul`
  padding: 0;
  margin: 0;
  position: relative;

  .search-container {
    position: absolute;
    top: -52px;
    left: 0;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .search {
    width: 320px;
  }

  .logLine {
    white-space: nowrap;
  }

  .list {
    scrollbar-color: dark;
  }
`;

interface AdvancedSearchParams {
  q?: string;
  regex?: boolean;
  caseSensitive?: boolean;
  channels?: string[];
  users?: string[];
  excludeUsers?: string[];
  messageTypes?: string[];
  badges?: string[];
  from?: Date | null;
  to?: Date | null;
  minLength?: number;
  maxLength?: number;
  firstMessagesOnly?: boolean;
  subscribersOnly?: boolean;
  vipsOnly?: boolean;
  moderatorsOnly?: boolean;
  hasEmotes?: boolean;
  searchDisplayNames?: boolean;
}

export function ContentLog({ year, month }: { year: string; month: string }) {
  const { state, setState } = useContext(store);
  const [searchText, setSearchText] = useState("");
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [advancedSearchParams, setAdvancedSearchParams] =
    useState<AdvancedSearchParams>({});
  const [isAdvancedSearchActive, setIsAdvancedSearchActive] = useState(false);

  const logs = useLog(
    state.currentChannel ?? "",
    state.currentUsername ?? "",
    year,
    month
  ).filter((log) => log.text.toLowerCase().includes(searchText.toLowerCase()));

  const {
    data: advancedSearchResults,
    isLoading: isAdvancedSearchLoading,
    error: advancedSearchError,
  } = useAdvancedSearch(
    state.currentChannel ?? "",
    state.currentUsername ?? "",
    advancedSearchParams,
    isAdvancedSearchActive
  );

  const Row = ({ index, style }: { index: number; style: CSSProperties }) => (
    <div style={style}>
      <LogLine
        key={logs[index].id ? logs[index].id : index}
        message={logs[index]}
      />
    </div>
  );

  const search = useRef<HTMLInputElement>(null);

  const handleMouseEnter = () => {
    setState({ ...state, activeSearchField: search.current });
  };

  const handleAdvancedSearch = (params: AdvancedSearchParams) => {
    setAdvancedSearchParams(params);
    setIsAdvancedSearchActive(true);
  };

  const handleClearAdvancedSearch = () => {
    setAdvancedSearchParams({});
    setIsAdvancedSearchActive(false);
  };

  useEffect(() => {
    setState({ ...state, activeSearchField: search.current });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <ContentLogContainer onMouseEnter={handleMouseEnter}>
      <Box className="search-container">
        <TextField
          className="search"
          label="Search"
          inputRef={search}
          onChange={(e) => setSearchText(e.target.value)}
          size="small"
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
          }}
        />
        <IconButton
          onClick={() => setShowAdvancedSearch(true)}
          title="Advanced Search"
          size="small"
          color={isAdvancedSearchActive ? "primary" : "default"}
        >
          <FilterList />
        </IconButton>
      </Box>

      {isAdvancedSearchActive && advancedSearchResults ? (
        <SearchResults
          results={advancedSearchResults}
          isLoading={isAdvancedSearchLoading}
          error={advancedSearchError}
          searchQuery={advancedSearchParams.q || ""}
        />
      ) : (
        <List
          className="list"
          height={600}
          itemCount={logs.length}
          itemSize={20}
          width={"100%"}
        >
          {Row}
        </List>
      )}

      <AdvancedSearch
        open={showAdvancedSearch}
        onClose={() => setShowAdvancedSearch(false)}
        onSearch={handleAdvancedSearch}
        initialParams={advancedSearchParams}
      />
    </ContentLogContainer>
  );
}
