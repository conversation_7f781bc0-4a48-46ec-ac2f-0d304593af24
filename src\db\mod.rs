mod migrations;
pub mod schema;
pub mod writer;
use std::collections::HashSet;

pub use migrations::run as setup_db;
use serde::Deserialize;
use writer::<PERSON>lushBuffer;

use crate::{
    error::Error,
    logs::{
        schema::LogRangeParams,
        stream::{FlushBufferResponse, LogsStream},
    },
    web::schema::{AvailableLogDate, ChannelLogsStats, LogsParams, PreviousName, UserLogsStats},
    Result,
};
use chrono::{DateTime, Datelike, Duration, Utc};
use clickhouse::{query::RowCursor, Client, Row};
use rand::{rng, seq::IteratorRandom};
use schema::StructuredMessage;
use tracing::debug;

const CHANNEL_MULTI_QUERY_SIZE_DAYS: i64 = 14;

pub async fn read_channel(
    db: &Client,
    channel_id: &str,
    params: LogsParams,
    flush_buffer: &FlushBuffer,
    (from, to): (DateTime<Utc>, DateTime<Utc>),
) -> Result<LogsStream> {
    let buffer_response =
        FlushBufferResponse::new(flush_buffer, channel_id, None, params, (from, to)).await;

    let suffix = if params.reverse { "DESC" } else { "ASC" };

    let mut query = format!("SELECT ?fields FROM message_structured WHERE channel_id = ? AND timestamp >= ? AND timestamp < ? ORDER BY timestamp {suffix}");

    if to - from > Duration::days(CHANNEL_MULTI_QUERY_SIZE_DAYS) {
        let count = db
            .query("SELECT count() FROM (SELECT timestamp FROM message_structured WHERE channel_id = ? AND timestamp >= ? AND timestamp < ? LIMIT 1)")
            .bind(channel_id)
            .bind(from.timestamp_millis() as f64 / 1000.0)
            .bind(to.timestamp_millis() as f64 / 1000.0)
            .fetch_one::<i32>().await?;
        if count == 0 {
            return Err(Error::NotFound);
        }

        let mut streams = Vec::with_capacity(1);

        let interval = Duration::days(CHANNEL_MULTI_QUERY_SIZE_DAYS);

        let mut current_from = from;
        let mut current_to = current_from + interval;

        loop {
            let cursor = next_cursor(db, &query, channel_id, current_from, current_to)?;
            streams.push(cursor);

            current_from += interval;
            current_to += interval;

            if current_to > to {
                let cursor = next_cursor(db, &query, channel_id, current_from, to)?;
                streams.push(cursor);
                break;
            }
        }

        if params.reverse {
            streams.reverse();
        }

        debug!("Using {} queries for multi-query stream", streams.len());

        LogsStream::new_multi_query(streams, buffer_response)
    } else {
        apply_limit_offset(&mut query, &buffer_response);

        let cursor = db
            .query(&query)
            .bind(channel_id)
            .bind(from.timestamp_millis() as f64 / 1000.0)
            .bind(to.timestamp_millis() as f64 / 1000.0)
            .fetch()?;
        LogsStream::new_cursor(cursor, buffer_response).await
    }
}

fn next_cursor(
    db: &Client,
    query: &str,
    channel_id: &str,
    from: DateTime<Utc>,
    to: DateTime<Utc>,
) -> Result<RowCursor<StructuredMessage<'static>>> {
    let cursor = db
        .query(query)
        .bind(channel_id)
        .bind(from.timestamp_millis() as f64 / 1000.0)
        .bind(to.timestamp_millis() as f64 / 1000.0)
        .fetch()?;
    Ok(cursor)
}

pub async fn read_user(
    db: &Client,
    channel_id: &str,
    user_id: &str,
    params: LogsParams,
    flush_buffer: &FlushBuffer,
    (from, to): (DateTime<Utc>, DateTime<Utc>),
) -> Result<LogsStream> {
    let buffer_response =
        FlushBufferResponse::new(flush_buffer, channel_id, Some(user_id), params, (from, to)).await;

    let suffix = if params.reverse { "DESC" } else { "ASC" };
    let mut query = format!("SELECT * FROM message_structured WHERE channel_id = ? AND user_id = ? AND timestamp >= ? AND timestamp < ? ORDER BY timestamp {suffix}");
    apply_limit_offset(&mut query, &buffer_response);

    let cursor = db
        .query(&query)
        .bind(channel_id)
        .bind(user_id)
        .bind(from.timestamp_millis() as f64 / 1000.0)
        .bind(to.timestamp_millis() as f64 / 1000.0)
        .fetch()?;
    LogsStream::new_cursor(cursor, buffer_response).await
}

pub async fn read_available_channel_logs(
    db: &Client,
    channel_id: &str,
) -> Result<Vec<AvailableLogDate>> {
    let timestamps: Vec<i32> = db
        .query(
            "SELECT toDateTime(toStartOfDay(timestamp)) AS date FROM message_structured WHERE channel_id = ? GROUP BY date ORDER BY date DESC",
        )
        .bind(channel_id)
        .fetch_all().await?;

    let dates = timestamps
        .into_iter()
        .map(|timestamp| {
            let naive = DateTime::from_timestamp(timestamp.into(), 0).expect("Invalid DateTime");

            AvailableLogDate {
                year: naive.year().to_string(),
                month: naive.month().to_string(),
                day: Some(naive.day().to_string()),
            }
        })
        .collect();

    Ok(dates)
}

pub async fn read_available_user_logs(
    db: &Client,
    channel_id: &str,
    user_id: &str,
) -> Result<Vec<AvailableLogDate>> {
    let timestamps: Vec<i32> = db
        .query("SELECT toDateTime(toStartOfMonth(timestamp)) AS date FROM message_structured WHERE channel_id = ? AND user_id = ? GROUP BY date ORDER BY date DESC")
        .bind(channel_id)
        .bind(user_id)
        .fetch_all().await?;

    let dates = timestamps
        .into_iter()
        .map(|timestamp| {
            let naive = DateTime::from_timestamp(timestamp.into(), 0).expect("Invalid DateTime");

            AvailableLogDate {
                year: naive.year().to_string(),
                month: naive.month().to_string(),
                day: None,
            }
        })
        .collect();

    Ok(dates)
}

pub async fn read_random_user_line(
    db: &Client,
    channel_id: &str,
    user_id: &str,
) -> Result<StructuredMessage<'static>> {
    let total_count = db
        .query("SELECT count(*) FROM message_structured WHERE channel_id = ? AND user_id = ? ")
        .bind(channel_id)
        .bind(user_id)
        .fetch_one::<u64>()
        .await?;

    if total_count == 0 {
        return Err(Error::NotFound);
    }

    let offset = {
        let mut rng = rng();
        (0..total_count).choose(&mut rng).ok_or(Error::NotFound)
    }?;

    let msg = db
        .query(
            "WITH
            (SELECT timestamp FROM message_structured WHERE channel_id = ? AND user_id = ? LIMIT 1 OFFSET ?)
            AS random_timestamp
            SELECT * FROM message_structured WHERE channel_id = ? AND user_id = ? AND timestamp = random_timestamp",
        )
        .bind(channel_id)
        .bind(user_id)
        .bind(offset)
        .bind(channel_id)
        .bind(user_id)
        .fetch_optional::<StructuredMessage>()
        .await?
        .ok_or(Error::NotFound)?;

    Ok(msg)
}

pub async fn read_random_channel_line(
    db: &Client,
    channel_id: &str,
) -> Result<StructuredMessage<'static>> {
    let total_count = db
        .query("SELECT count(*) FROM message_structured WHERE channel_id = ? ")
        .bind(channel_id)
        .fetch_one::<u64>()
        .await?;

    if total_count == 0 {
        return Err(Error::NotFound);
    }

    let offset = {
        let mut rng = rng();
        (0..total_count).choose(&mut rng).ok_or(Error::NotFound)
    }?;

    let msg = db
        .query(
            "WITH
            (SELECT timestamp FROM message_structured WHERE channel_id = ? LIMIT 1 OFFSET ?)
            AS random_timestamp
            SELECT * FROM message_structured WHERE channel_id = ? AND timestamp = random_timestamp",
        )
        .bind(channel_id)
        .bind(offset)
        .bind(channel_id)
        .fetch_optional::<StructuredMessage>()
        .await?
        .ok_or(Error::NotFound)?;

    Ok(msg)
}

pub async fn delete_user_logs(_db: &Client, _user_id: &str) -> Result<()> {
    // info!("Deleting all logs for user {user_id}");
    // db.query("ALTER TABLE message DELETE WHERE user_id = ?")
    //     .bind(user_id)
    //     .execute()
    //     .await?;
    Ok(())
}

pub async fn search_user_logs(
    db: &Client,
    channel_id: &str,
    user_id: &str,
    search: &str,
    params: LogsParams,
) -> Result<LogsStream> {
    let buffer_response = FlushBufferResponse::empty(params);

    let suffix = if params.reverse { "DESC" } else { "ASC" };

    let mut query = format!("SELECT * FROM message_structured WHERE channel_id = ? AND user_id = ? AND positionCaseInsensitive(text, ?) != 0 ORDER BY timestamp {suffix}");
    apply_limit_offset(&mut query, &buffer_response);

    let cursor = db
        .query(&query)
        .bind(channel_id)
        .bind(user_id)
        .bind(search)
        .fetch()?;

    LogsStream::new_cursor(cursor, buffer_response).await
}

pub async fn advanced_search_logs(
    db: &Client,
    channel_id: &str,
    search_params: &crate::web::schema::AdvancedSearchParams,
    params: LogsParams,
) -> Result<LogsStream> {
    let buffer_response = FlushBufferResponse::empty(params);
    let suffix = if params.reverse { "DESC" } else { "ASC" };

    let mut query = "SELECT * FROM message_structured WHERE channel_id = ?".to_string();
    let mut bind_values: Vec<String> = vec![channel_id.to_string()];

    // Text search
    if let Some(ref q) = search_params.q {
        if !q.is_empty() {
            if search_params.search_display_names {
                // Search in display names instead of message text
                if search_params.regex {
                    if search_params.case_sensitive {
                        query.push_str(" AND match(user_display_name, ?)");
                    } else {
                        query.push_str(" AND match(user_display_name, concat('(?i)', ?))");
                    }
                } else {
                    if search_params.case_sensitive {
                        query.push_str(" AND position(user_display_name, ?) != 0");
                    } else {
                        query.push_str(" AND positionCaseInsensitive(user_display_name, ?) != 0");
                    }
                }
            } else {
                // Search in message text (default behavior)
                if search_params.regex {
                    if search_params.case_sensitive {
                        query.push_str(" AND match(text, ?)");
                    } else {
                        query.push_str(" AND match(text, concat('(?i)', ?))");
                    }
                } else {
                    if search_params.case_sensitive {
                        query.push_str(" AND position(text, ?) != 0");
                    } else {
                        query.push_str(" AND positionCaseInsensitive(text, ?) != 0");
                    }
                }
            }
            bind_values.push(q.clone());
        }
    }

    // User filtering
    if let Some(ref users) = search_params.users {
        let user_list: Vec<&str> = users.split(',').map(|s| s.trim()).collect();
        if !user_list.is_empty() {
            let placeholders = user_list.iter().map(|_| "?").collect::<Vec<_>>().join(",");
            query.push_str(&format!(" AND (user_login IN ({}) OR user_id IN ({}))", placeholders, placeholders));
            for user in &user_list {
                bind_values.push(user.to_string());
            }
            for user in &user_list {
                bind_values.push(user.to_string());
            }
        }
    }

    // Exclude users
    if let Some(ref exclude_users) = search_params.exclude_users {
        let user_list: Vec<&str> = exclude_users.split(',').map(|s| s.trim()).collect();
        if !user_list.is_empty() {
            let placeholders = user_list.iter().map(|_| "?").collect::<Vec<_>>().join(",");
            query.push_str(&format!(" AND user_login NOT IN ({}) AND user_id NOT IN ({})", placeholders, placeholders));
            for user in &user_list {
                bind_values.push(user.to_string());
            }
            for user in &user_list {
                bind_values.push(user.to_string());
            }
        }
    }

    // Message type filtering
    if let Some(ref message_types) = search_params.message_types {
        let types: Vec<&str> = message_types.split(',').map(|s| s.trim()).collect();
        if !types.is_empty() {
            let mut type_conditions = Vec::new();
            for msg_type in types {
                match msg_type.to_lowercase().as_str() {
                    "privmsg" => type_conditions.push("message_type = 1"),
                    "notice" => type_conditions.push("message_type = 6"),
                    "clearchat" => type_conditions.push("message_type = 2"),
                    "usernotice" => type_conditions.push("message_type = 4"),
                    "whisper" => type_conditions.push("message_type = 0"),
                    _ => {}
                }
            }
            if !type_conditions.is_empty() {
                query.push_str(&format!(" AND ({})", type_conditions.join(" OR ")));
            }
        }
    }

    // Date range filtering
    if let Some(from) = search_params.from {
        query.push_str(" AND timestamp >= ?");
        bind_values.push((from.timestamp_millis() as f64 / 1000.0).to_string());
    }

    if let Some(to) = search_params.to {
        query.push_str(" AND timestamp < ?");
        bind_values.push((to.timestamp_millis() as f64 / 1000.0).to_string());
    }

    // Message length filtering
    if let Some(min_length) = search_params.min_length {
        query.push_str(" AND length(text) >= ?");
        bind_values.push(min_length.to_string());
    }

    if let Some(max_length) = search_params.max_length {
        query.push_str(" AND length(text) <= ?");
        bind_values.push(max_length.to_string());
    }

    // Badge filtering using message_flags
    if search_params.subscribers_only {
        query.push_str(" AND (message_flags & 1) != 0"); // SUBSCRIBER flag
    }

    if search_params.vips_only {
        query.push_str(" AND (message_flags & 2) != 0"); // VIP flag
    }

    if search_params.moderators_only {
        query.push_str(" AND (message_flags & 4) != 0"); // MOD flag
    }

    if search_params.first_messages_only {
        query.push_str(" AND (message_flags & 16) != 0"); // FIRST_MSG flag
    }

    // Additional badge filtering
    if let Some(ref badges) = search_params.badges {
        let badge_list: Vec<&str> = badges.split(',').map(|s| s.trim()).collect();
        if !badge_list.is_empty() {
            let mut badge_conditions = Vec::new();
            for badge in badge_list {
                match badge.to_lowercase().as_str() {
                    "subscriber" => badge_conditions.push("(message_flags & 1) != 0"),
                    "vip" => badge_conditions.push("(message_flags & 2) != 0"),
                    "moderator" | "mod" => badge_conditions.push("(message_flags & 4) != 0"),
                    "broadcaster" => badge_conditions.push("(message_flags & 8) != 0"),
                    "first_msg" => badge_conditions.push("(message_flags & 16) != 0"),
                    _ => {}
                }
            }
            if !badge_conditions.is_empty() {
                query.push_str(&format!(" AND ({})", badge_conditions.join(" OR ")));
            }
        }
    }

    // Emotes filtering
    if search_params.has_emotes {
        query.push_str(" AND emotes != ''");
    }

    query.push_str(&format!(" ORDER BY timestamp {}", suffix));
    apply_limit_offset(&mut query, &buffer_response);

    let mut db_query = db.query(&query);
    for value in bind_values {
        db_query = db_query.bind(value);
    }

    let cursor = db_query.fetch()?;
    LogsStream::new_cursor(cursor, buffer_response).await
}

pub async fn search_channel_logs(
    db: &Client,
    channel_id: &str,
    search: &str,
    params: LogsParams,
) -> Result<LogsStream> {
    let buffer_response = FlushBufferResponse::empty(params);
    let suffix = if params.reverse { "DESC" } else { "ASC" };

    let mut query = format!("SELECT * FROM message_structured WHERE channel_id = ? AND positionCaseInsensitive(text, ?) != 0 ORDER BY timestamp {suffix}");
    apply_limit_offset(&mut query, &buffer_response);

    let cursor = db
        .query(&query)
        .bind(channel_id)
        .bind(search)
        .fetch()?;

    LogsStream::new_cursor(cursor, buffer_response).await
}

pub async fn global_search_logs(
    db: &Client,
    search: &str,
    params: LogsParams,
) -> Result<LogsStream> {
    let buffer_response = FlushBufferResponse::empty(params);
    let suffix = if params.reverse { "DESC" } else { "ASC" };

    let (_query, cursor) = if search.is_empty() {
        let query = format!("SELECT * FROM message_structured ORDER BY timestamp {suffix}");
        let mut query_with_limit = query.clone();
        apply_limit_offset(&mut query_with_limit, &buffer_response);

        let cursor = db.query(&query_with_limit).fetch()?;
        (query_with_limit, cursor)
    } else {
        let query = format!("SELECT * FROM message_structured WHERE position(text, ?) > 0 ORDER BY timestamp {suffix}");
        let mut query_with_limit = query.clone();
        apply_limit_offset(&mut query_with_limit, &buffer_response);

        let cursor = db
            .query(&query_with_limit)
            .bind(search)
            .fetch()?;
        (query_with_limit, cursor)
    };

    LogsStream::new_cursor(cursor, buffer_response).await
}

pub async fn global_advanced_search_logs(
    db: &Client,
    search_params: &crate::web::schema::AdvancedSearchParams,
    params: LogsParams,
) -> Result<LogsStream> {
    let buffer_response = FlushBufferResponse::empty(params);
    let suffix = if params.reverse { "DESC" } else { "ASC" };

    let mut query = "SELECT * FROM message_structured WHERE 1=1".to_string();
    let mut bind_values: Vec<String> = vec![];

    // Channel filtering (new for global search)
    if let Some(ref channels) = search_params.channels {
        let channel_list: Vec<&str> = channels.split(',').map(|s| s.trim()).collect();
        if !channel_list.is_empty() {
            let placeholders = channel_list.iter().map(|_| "?").collect::<Vec<_>>().join(",");
            query.push_str(&format!(" AND (channel_login IN ({}) OR channel_id IN ({}))", placeholders, placeholders));
            for channel in &channel_list {
                bind_values.push(channel.to_string());
            }
            for channel in &channel_list {
                bind_values.push(channel.to_string());
            }
        }
    }

    // Text search
    if let Some(ref q) = search_params.q {
        if !q.is_empty() {
            if search_params.search_display_names {
                // Search in display names instead of message text
                if search_params.regex {
                    if search_params.case_sensitive {
                        query.push_str(" AND match(display_name, ?)");
                    } else {
                        query.push_str(" AND match(display_name, concat('(?i)', ?))");
                    }
                } else {
                    if search_params.case_sensitive {
                        query.push_str(" AND position(display_name, ?) != 0");
                    } else {
                        query.push_str(" AND positionCaseInsensitive(display_name, ?) != 0");
                    }
                }
            } else {
                // Search in message text (default behavior)
                if search_params.regex {
                    if search_params.case_sensitive {
                        query.push_str(" AND match(text, ?)");
                    } else {
                        query.push_str(" AND match(text, concat('(?i)', ?))");
                    }
                } else {
                    if search_params.case_sensitive {
                        query.push_str(" AND position(text, ?) != 0");
                    } else {
                        query.push_str(" AND positionCaseInsensitive(text, ?) != 0");
                    }
                }
            }
            bind_values.push(q.clone());
        }
    }

    // User filtering
    if let Some(ref users) = search_params.users {
        let user_list: Vec<&str> = users.split(',').map(|s| s.trim()).collect();
        if !user_list.is_empty() {
            let placeholders = user_list.iter().map(|_| "?").collect::<Vec<_>>().join(",");
            query.push_str(&format!(" AND (user_login IN ({}) OR user_id IN ({}))", placeholders, placeholders));
            for user in &user_list {
                bind_values.push(user.to_string());
            }
            for user in &user_list {
                bind_values.push(user.to_string());
            }
        }
    }

    // Exclude users
    if let Some(ref exclude_users) = search_params.exclude_users {
        let user_list: Vec<&str> = exclude_users.split(',').map(|s| s.trim()).collect();
        if !user_list.is_empty() {
            let placeholders = user_list.iter().map(|_| "?").collect::<Vec<_>>().join(",");
            query.push_str(&format!(" AND user_login NOT IN ({}) AND user_id NOT IN ({})", placeholders, placeholders));
            for user in &user_list {
                bind_values.push(user.to_string());
            }
            for user in &user_list {
                bind_values.push(user.to_string());
            }
        }
    }

    // Message type filtering
    if let Some(ref message_types) = search_params.message_types {
        let types: Vec<&str> = message_types.split(',').map(|s| s.trim()).collect();
        if !types.is_empty() {
            let mut type_conditions = Vec::new();
            for msg_type in types {
                match msg_type.to_lowercase().as_str() {
                    "privmsg" => type_conditions.push("message_type = 1"),
                    "notice" => type_conditions.push("message_type = 6"),
                    "clearchat" => type_conditions.push("message_type = 2"),
                    "usernotice" => type_conditions.push("message_type = 4"),
                    "whisper" => type_conditions.push("message_type = 0"),
                    _ => {}
                }
            }
            if !type_conditions.is_empty() {
                query.push_str(&format!(" AND ({})", type_conditions.join(" OR ")));
            }
        }
    }

    // Date range filtering
    if let Some(from) = search_params.from {
        query.push_str(" AND timestamp >= ?");
        bind_values.push((from.timestamp_millis() as f64 / 1000.0).to_string());
    }

    if let Some(to) = search_params.to {
        query.push_str(" AND timestamp < ?");
        bind_values.push((to.timestamp_millis() as f64 / 1000.0).to_string());
    }

    // Message length filtering
    if let Some(min_length) = search_params.min_length {
        query.push_str(" AND length(text) >= ?");
        bind_values.push(min_length.to_string());
    }

    if let Some(max_length) = search_params.max_length {
        query.push_str(" AND length(text) <= ?");
        bind_values.push(max_length.to_string());
    }

    // Badge filtering using message_flags
    if search_params.subscribers_only {
        query.push_str(" AND bitAnd(message_flags, 1) != 0"); // SUBSCRIBER flag
    }

    if search_params.vips_only {
        query.push_str(" AND bitAnd(message_flags, 2) != 0"); // VIP flag
    }

    if search_params.moderators_only {
        query.push_str(" AND bitAnd(message_flags, 4) != 0"); // MOD flag
    }

    if search_params.first_messages_only {
        query.push_str(" AND bitAnd(message_flags, 16) != 0"); // FIRST_MSG flag
    }

    // Additional badge filtering
    if let Some(ref badges) = search_params.badges {
        let badge_list: Vec<&str> = badges.split(',').map(|s| s.trim()).collect();
        if !badge_list.is_empty() {
            let mut badge_conditions = Vec::new();
            for badge in badge_list {
                match badge.to_lowercase().as_str() {
                    "subscriber" => badge_conditions.push("bitAnd(message_flags, 1) != 0"),
                    "vip" => badge_conditions.push("bitAnd(message_flags, 2) != 0"),
                    "moderator" | "mod" => badge_conditions.push("bitAnd(message_flags, 4) != 0"),
                    "broadcaster" => badge_conditions.push("bitAnd(message_flags, 8) != 0"),
                    "first_msg" => badge_conditions.push("bitAnd(message_flags, 16) != 0"),
                    _ => {}
                }
            }
            if !badge_conditions.is_empty() {
                query.push_str(&format!(" AND ({})", badge_conditions.join(" OR ")));
            }
        }
    }

    // Emotes filtering
    if search_params.has_emotes {
        query.push_str(" AND emotes != ''");
    }

    query.push_str(&format!(" ORDER BY timestamp {}", suffix));
    apply_limit_offset(&mut query, &buffer_response);

    let mut db_query = db.query(&query);
    for value in bind_values {
        db_query = db_query.bind(value);
    }

    let cursor = db_query.fetch()?;
    LogsStream::new_cursor(cursor, buffer_response).await
}

pub async fn get_global_analytics(
    db: &Client,
    params: &crate::web::schema::AnalyticsParams,
) -> Result<crate::web::schema::GlobalAnalytics> {
    use crate::web::schema::*;

    // Default date range: last 30 days
    let to_date = params.to.unwrap_or_else(|| chrono::Utc::now());
    let from_date = params.from.unwrap_or_else(|| to_date - chrono::Duration::days(30));

    // Get total counts
    let total_stats = db
        .query("SELECT
            COUNT(DISTINCT channel_id) as total_channels,
            COUNT(*) as total_messages,
            COUNT(DISTINCT user_id) as total_users
        FROM message_structured
        WHERE timestamp >= ? AND timestamp < ?")
        .bind(from_date.timestamp_millis() as f64 / 1000.0)
        .bind(to_date.timestamp_millis() as f64 / 1000.0)
        .fetch_one::<(u64, u64, u64)>()
        .await?;

    // Get top channels
    let top_channels_data = db
        .query("SELECT
            channel_id,
            channel_login,
            COUNT(*) as message_count,
            COUNT(DISTINCT user_id) as unique_users,
            COUNT(*) / dateDiff('day', toDate(?), toDate(?)) as avg_daily_messages
        FROM message_structured
        WHERE timestamp >= ? AND timestamp < ?
        GROUP BY channel_id, channel_login
        ORDER BY message_count DESC
        LIMIT ?")
        .bind(from_date.timestamp_millis() as f64 / 1000.0)
        .bind(to_date.timestamp_millis() as f64 / 1000.0)
        .bind(from_date.timestamp_millis() as f64 / 1000.0)
        .bind(to_date.timestamp_millis() as f64 / 1000.0)
        .bind(params.limit.unwrap_or(10))
        .fetch_all::<(String, String, u64, u64, f64)>()
        .await?;

    let top_channels = top_channels_data
        .into_iter()
        .map(|(channel_id, channel_name, message_count, unique_users, avg_daily_messages)| {
            ChannelSummary {
                channel_id,
                channel_name,
                message_count,
                unique_users,
                avg_daily_messages,
            }
        })
        .collect();

    // Get top users
    let top_users_data = db
        .query("SELECT
            user_id,
            user_login,
            COUNT(*) as message_count,
            COUNT(DISTINCT channel_id) as channels_active,
            AVG(length(text)) as avg_message_length
        FROM message_structured
        WHERE timestamp >= ? AND timestamp < ?
        GROUP BY user_id, user_login
        ORDER BY message_count DESC
        LIMIT ?")
        .bind(from_date.timestamp_millis() as f64 / 1000.0)
        .bind(to_date.timestamp_millis() as f64 / 1000.0)
        .bind(params.limit.unwrap_or(10))
        .fetch_all::<(String, String, u64, u64, f64)>()
        .await?;

    let top_users = top_users_data
        .into_iter()
        .map(|(user_id, username, message_count, channels_active, avg_message_length)| {
            UserSummary {
                user_id,
                username,
                message_count,
                channels_active,
                avg_message_length,
            }
        })
        .collect();

    // Get message type distribution
    let message_types_data = db
        .query("SELECT
            message_type,
            COUNT(*) as count
        FROM message_structured
        WHERE timestamp >= ? AND timestamp < ?
        GROUP BY message_type
        ORDER BY count DESC")
        .bind(from_date.timestamp_millis() as f64 / 1000.0)
        .bind(to_date.timestamp_millis() as f64 / 1000.0)
        .fetch_all::<(u8, u64)>()
        .await?;

    let total_messages_for_percentage = total_stats.1;
    let message_distribution = message_types_data
        .into_iter()
        .map(|(msg_type, count)| {
            let message_type = match msg_type {
                0 => "whisper",
                1 => "privmsg",
                2 => "clearchat",
                4 => "usernotice",
                6 => "notice",
                _ => "other",
            }.to_string();

            MessageTypeCount {
                message_type,
                count,
                percentage: (count as f64 / total_messages_for_percentage as f64) * 100.0,
            }
        })
        .collect();

    // Get hourly activity heatmap
    let hourly_data = db
        .query("SELECT
            toHour(timestamp) as hour,
            COUNT(*) as message_count,
            COUNT(DISTINCT user_id) as unique_users,
            AVG(length(text)) as avg_message_length
        FROM message_structured
        WHERE timestamp >= ? AND timestamp < ?
        GROUP BY hour
        ORDER BY hour")
        .bind(from_date.timestamp_millis() as f64 / 1000.0)
        .bind(to_date.timestamp_millis() as f64 / 1000.0)
        .fetch_all::<(u8, u64, u64, f64)>()
        .await?;

    let activity_heatmap = hourly_data
        .into_iter()
        .map(|(hour, message_count, unique_users, avg_message_length)| {
            HourlyActivity {
                hour,
                message_count,
                unique_users,
                avg_message_length,
            }
        })
        .collect();

    // Calculate growth metrics (simplified)
    let days_diff = (to_date - from_date).num_days() as f64;
    let growth_metrics = GrowthMetrics {
        daily_growth_rate: 0.0, // Would need historical comparison
        weekly_growth_rate: 0.0,
        monthly_growth_rate: 0.0,
        new_users_per_day: total_stats.2 as f64 / days_diff,
        retention_rate: 0.0, // Would need user session analysis
    };

    Ok(GlobalAnalytics {
        total_channels: total_stats.0,
        total_messages: total_stats.1,
        total_users: total_stats.2,
        date_range: DateRange {
            start: from_date,
            end: to_date,
        },
        top_channels,
        top_users,
        message_distribution,
        activity_heatmap,
        growth_metrics,
    })
}

pub async fn get_channel_analytics(
    db: &Client,
    channel_id: &str,
    params: &crate::web::schema::AnalyticsParams,
) -> Result<crate::web::schema::ChannelAnalytics> {
    use crate::web::schema::*;

    // Default date range: last 30 days
    let to_date = params.to.unwrap_or_else(|| chrono::Utc::now());
    let from_date = params.from.unwrap_or_else(|| to_date - chrono::Duration::days(30));

    // Get basic channel stats
    let channel_stats = db
        .query("SELECT
            channel_login,
            COUNT(*) as total_messages,
            COUNT(DISTINCT user_id) as unique_users,
            COUNT(*) / dateDiff('day', toDate(?), toDate(?)) as avg_messages_per_day
        FROM message_structured
        WHERE channel_id = ? AND timestamp >= ? AND timestamp < ?
        GROUP BY channel_login")
        .bind(from_date.timestamp_millis() as f64 / 1000.0)
        .bind(to_date.timestamp_millis() as f64 / 1000.0)
        .bind(channel_id)
        .bind(from_date.timestamp_millis() as f64 / 1000.0)
        .bind(to_date.timestamp_millis() as f64 / 1000.0)
        .fetch_optional::<(String, u64, u64, f64)>()
        .await?;

    // If no data found, return empty analytics
    let (channel_login, total_messages, unique_users, avg_messages_per_day) = match channel_stats {
        Some(stats) => stats,
        None => {
            // Return empty analytics for channels with no data
            return Ok(crate::web::schema::ChannelAnalytics {
                channel_id: channel_id.to_string(),
                channel_name: channel_id.to_string(), // fallback to channel_id
                total_messages: 0,
                unique_users: 0,
                avg_messages_per_day: 0.0,
                peak_activity_hour: 0,
                top_users: vec![],
                message_types: vec![],
                activity_timeline: vec![],
            });
        }
    };

    // Get peak activity hour
    let peak_hour = db
        .query("SELECT
            toHour(timestamp) as hour,
            COUNT(*) as count
        FROM message_structured
        WHERE channel_id = ? AND timestamp >= ? AND timestamp < ?
        GROUP BY hour
        ORDER BY count DESC
        LIMIT 1")
        .bind(channel_id)
        .bind(from_date.timestamp_millis() as f64 / 1000.0)
        .bind(to_date.timestamp_millis() as f64 / 1000.0)
        .fetch_optional::<(u8, u64)>()
        .await?
        .map(|(hour, _)| hour)
        .unwrap_or(0);

    // Get top users
    let top_users_data = db
        .query("SELECT
            user_id,
            user_login,
            COUNT(*) as message_count,
            MIN(timestamp) as first_seen,
            MAX(timestamp) as last_seen,
            AVG(length(text)) as avg_message_length,
            MAX(bitAnd(message_flags, 1) != 0) as is_subscriber,
            MAX(bitAnd(message_flags, 2) != 0) as is_vip,
            MAX(bitAnd(message_flags, 4) != 0) as is_moderator
        FROM message_structured
        WHERE channel_id = ? AND timestamp >= ? AND timestamp < ?
        GROUP BY user_id, user_login
        ORDER BY message_count DESC
        LIMIT ?")
        .bind(channel_id)
        .bind(from_date.timestamp_millis() as f64 / 1000.0)
        .bind(to_date.timestamp_millis() as f64 / 1000.0)
        .bind(params.limit.unwrap_or(10))
        .fetch_all::<(String, String, u64, f64, f64, f64, u8, u8, u8)>()
        .await?;

    let top_users = top_users_data
        .into_iter()
        .map(|(user_id, username, message_count, first_seen, last_seen, avg_message_length, is_sub, is_vip, is_mod)| {
            UserActivity {
                user_id,
                username,
                message_count,
                first_seen: chrono::DateTime::from_timestamp((first_seen / 1000.0) as i64, 0).unwrap_or_default(),
                last_seen: chrono::DateTime::from_timestamp((last_seen / 1000.0) as i64, 0).unwrap_or_default(),
                avg_message_length,
                is_subscriber: is_sub != 0,
                is_vip: is_vip != 0,
                is_moderator: is_mod != 0,
            }
        })
        .collect();

    // Get message types
    let message_types_data = db
        .query("SELECT
            message_type,
            COUNT(*) as count
        FROM message_structured
        WHERE channel_id = ? AND timestamp >= ? AND timestamp < ?
        GROUP BY message_type
        ORDER BY count DESC")
        .bind(channel_id)
        .bind(from_date.timestamp_millis() as f64 / 1000.0)
        .bind(to_date.timestamp_millis() as f64 / 1000.0)
        .fetch_all::<(u8, u64)>()
        .await?;

    let total_messages_for_percentage = total_messages;
    let message_types = message_types_data
        .into_iter()
        .map(|(msg_type, count)| {
            let message_type = match msg_type {
                0 => "whisper",
                1 => "privmsg",
                2 => "clearchat",
                4 => "usernotice",
                6 => "notice",
                _ => "other",
            }.to_string();

            MessageTypeCount {
                message_type,
                count,
                percentage: (count as f64 / total_messages_for_percentage as f64) * 100.0,
            }
        })
        .collect();

    // Get activity timeline based on granularity
    let granularity = params.granularity.as_deref().unwrap_or("day");
    let time_format = match granularity {
        "hour" => "toStartOfHour(timestamp)",
        "day" => "toStartOfDay(timestamp)",
        "week" => "toStartOfWeek(timestamp)",
        "month" => "toStartOfMonth(timestamp)",
        _ => "toStartOfDay(timestamp)",
    };

    let activity_data = db
        .query(&format!("SELECT
            {} as time_bucket,
            COUNT(*) as message_count,
            COUNT(DISTINCT user_id) as unique_users
        FROM message_structured
        WHERE channel_id = ? AND timestamp >= ? AND timestamp < ?
        GROUP BY time_bucket
        ORDER BY time_bucket", time_format))
        .bind(channel_id)
        .bind(from_date.timestamp_millis() as f64 / 1000.0)
        .bind(to_date.timestamp_millis() as f64 / 1000.0)
        .fetch_all::<(f64, u64, u64)>()
        .await?;

    let activity_timeline = activity_data
        .into_iter()
        .map(|(timestamp, message_count, unique_users)| {
            ActivityPoint {
                timestamp: chrono::DateTime::from_timestamp((timestamp / 1000.0) as i64, 0).unwrap_or_default(),
                message_count,
                unique_users,
            }
        })
        .collect();

    Ok(ChannelAnalytics {
        channel_id: channel_id.to_string(),
        channel_name: channel_login,
        total_messages,
        unique_users,
        avg_messages_per_day,
        peak_activity_hour: peak_hour,
        top_users,
        message_types,
        activity_timeline,
    })
}

pub async fn get_channel_stats(
    db: &Client,
    channel_id: &str,
    range_params: LogRangeParams,
) -> Result<ChannelLogsStats> {
    #[derive(Deserialize, Row)]
    struct StatsRow {
        pub cnt: u64,
        pub user_id: String,
    }

    let mut query = "SELECT count(*) FROM message_structured WHERE channel_id = ?".to_owned();

    if range_params.range().is_some() {
        query.push_str(" AND timestamp >= ? AND timestamp < ?");
    }

    let mut query = db.query(&query).bind(channel_id);

    if let Some((from, to)) = range_params.range() {
        query = query
            .bind(from.timestamp_millis() as f64 / 1000.0)
            .bind(to.timestamp_millis() as f64 / 1000.0);
    }

    let total_count = query.fetch_one().await?;

    let mut query =
        "SELECT count(*) as cnt, user_id FROM message_structured WHERE channel_id = ? AND user_id != ''".to_owned();

    if range_params.range().is_some() {
        query.push_str(" AND timestamp >= ? AND timestamp < ?");
    }

    query.push_str(" GROUP BY user_id ORDER BY cnt DESC LIMIT 5 SETTINGS use_query_cache = 1, query_cache_ttl = 300");

    let mut query = db.query(&query).bind(channel_id);

    if let Some((from, to)) = range_params.range() {
        query = query
            .bind(from.timestamp_millis() as f64 / 1000.0)
            .bind(to.timestamp_millis() as f64 / 1000.0);
    }

    let stats_rows = query.fetch_all::<StatsRow>().await?;
    let top_chatters = stats_rows
        .into_iter()
        .map(|row| UserLogsStats {
            user_id: row.user_id,
            message_count: row.cnt,
        })
        .collect();

    Ok(ChannelLogsStats {
        message_count: total_count,
        top_chatters,
    })
}

pub async fn get_user_stats(
    db: &Client,
    channel_id: &str,
    user_id: &str,
    range_params: LogRangeParams,
) -> Result<UserLogsStats> {
    let mut query =
        "SELECT count(*) FROM message_structured WHERE channel_id = ? AND user_id = ?".to_owned();

    if range_params.range().is_some() {
        query.push_str(" AND timestamp >= ? AND timestamp < ?");
    }

    let mut query = db.query(&query).bind(channel_id).bind(user_id);

    if let Some((from, to)) = range_params.range() {
        query = query
            .bind(from.timestamp_millis() as f64 / 1000.0)
            .bind(to.timestamp_millis() as f64 / 1000.0);
    }

    let count = query.fetch_one().await?;

    Ok(UserLogsStats {
        message_count: count,
        user_id: user_id.to_owned(),
    })
}

pub async fn get_user_name_history(db: &Client, user_id: &str) -> Result<Vec<PreviousName>> {
    #[derive(Deserialize, Row)]
    struct SingleNameHistory {
        user_login: String,
        last_timestamp: i64,
        first_timestamp: i64,
    }

    let query = "
        SELECT trim(LEADING ':' FROM user_login) as user_login,
        max(last_timestamp) AS last_timestamp,
        min(first_timestamp) AS first_timestamp
        FROM username_history
        WHERE user_id = ?
        GROUP BY user_login";

    let name_history_rows: Vec<SingleNameHistory> =
        db.query(query).bind(user_id).fetch_all().await?;

    let mut seen_logins = HashSet::new();

    let names = name_history_rows
        .into_iter()
        .filter_map(|row| {
            if seen_logins.insert(row.user_login.clone()) {
                Some(PreviousName {
                    user_login: row.user_login,
                    last_timestamp: DateTime::from_timestamp_millis(row.last_timestamp)
                        .expect("Invalid DateTime"),
                    first_timestamp: DateTime::from_timestamp_millis(row.first_timestamp)
                        .expect("Invalid DateTime"),
                })
            } else {
                None
            }
        })
        .collect();

    Ok(names)
}

pub async fn get_social_network_analysis(
    db: &Client,
    params: &crate::web::schema::AnalyticsParams,
) -> Result<crate::web::schema::SocialNetworkAnalysis> {
    use crate::web::schema::*;

    // Default date range: last 7 days for performance
    let to_date = params.to.unwrap_or_else(|| chrono::Utc::now());
    let from_date = params.from.unwrap_or_else(|| to_date - chrono::Duration::days(7));

    // Get influence metrics based on message patterns
    let influence_data = db
        .query("SELECT
            user_id,
            user_login,
            COUNT(*) as message_count,
            COUNT(DISTINCT channel_id) as channel_reach,
            AVG(length(text)) as avg_message_length
        FROM message_structured
        WHERE timestamp >= ? AND timestamp < ?
        AND message_type = 1
        GROUP BY user_id, user_login
        HAVING message_count >= 10
        ORDER BY message_count DESC
        LIMIT ?")
        .bind(from_date.timestamp_millis() as f64 / 1000.0)
        .bind(to_date.timestamp_millis() as f64 / 1000.0)
        .bind(params.limit.unwrap_or(50))
        .fetch_all::<(String, String, u64, u64, f64)>()
        .await?;

    let influence_metrics = influence_data
        .into_iter()
        .map(|(user_id, username, message_count, channel_reach, avg_message_length)| {
            // Simple influence score calculation
            let influence_score = (message_count as f64).ln() + (channel_reach as f64 * 2.0) + (avg_message_length / 10.0);

            InfluenceMetric {
                user_id,
                username,
                influence_score,
                reach: channel_reach,
                engagement_rate: 0.0, // Would need response analysis
                response_rate: 0.0,   // Would need temporal analysis
            }
        })
        .collect();

    // Simplified user interactions (users active in same channels)
    let interactions_data = db
        .query("SELECT
            a.user_id as user_a,
            b.user_id as user_b,
            COUNT(DISTINCT a.channel_id) as shared_channels,
            COUNT(*) as interaction_count
        FROM message_structured a
        JOIN message_structured b ON
            a.channel_id = b.channel_id
            AND a.user_id < b.user_id  -- Avoid duplicates
        WHERE a.timestamp >= ? AND a.timestamp < ?
        AND b.timestamp >= ? AND b.timestamp < ?
        AND a.message_type = 1 AND b.message_type = 1
        GROUP BY a.user_id, b.user_id
        HAVING shared_channels >= 2 OR interaction_count >= 50
        ORDER BY interaction_count DESC
        LIMIT ?")
        .bind(from_date.timestamp_millis() as f64 / 1000.0)
        .bind(to_date.timestamp_millis() as f64 / 1000.0)
        .bind(from_date.timestamp_millis() as f64 / 1000.0)
        .bind(to_date.timestamp_millis() as f64 / 1000.0)
        .bind(params.limit.unwrap_or(100))
        .fetch_all::<(String, String, u64, u64)>()
        .await?;

    let user_interactions = interactions_data
        .into_iter()
        .map(|(user_a, user_b, shared_channels, interaction_count)| {
            UserInteraction {
                user_a,
                user_b,
                interaction_count,
                interaction_strength: (shared_channels as f64 * interaction_count as f64).sqrt(),
                channels: vec![], // Would need additional query
            }
        })
        .collect();

    // Placeholder for more complex analysis
    let community_clusters = vec![];
    let conversation_threads = vec![];

    Ok(SocialNetworkAnalysis {
        user_interactions,
        community_clusters,
        influence_metrics,
        conversation_threads,
    })
}

fn apply_limit_offset(query: &mut String, buffer_response: &FlushBufferResponse) {
    if let Some(limit) = buffer_response.normalized_limit() {
        *query = format!("{query} LIMIT {limit}");
    }
    if let Some(offset) = buffer_response.normalized_offset() {
        *query = format!("{query} OFFSET {offset}");
    }
}
