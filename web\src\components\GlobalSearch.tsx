import { Input<PERSON><PERSON>n<PERSON>, <PERSON>Field, IconButton, Box, Typography, Paper, Switch, FormControlLabel } from "@mui/material";
import { Search, FilterList, Public } from "@mui/icons-material";
import React, { useState, useRef, useEffect } from "react";
import styled from "styled-components";
import { useGlobalSearch } from "../hooks/useGlobalSearch";
import { AdvancedSearch } from "./AdvancedSearch";
import { SearchResults } from "./SearchResults";
import dayjs, { Dayjs } from "dayjs";

const GlobalSearchContainer = styled.div`
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .search-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 24px;
  }

  .search-container {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
  }

  .search {
    width: 400px;
  }

  .info-panel {
    padding: 16px;
    margin-bottom: 16px;
    background: var(--bg-bright);
    border-radius: 8px;
  }
`;

interface AdvancedSearchParams {
  q?: string;
  regex?: boolean;
  caseSensitive?: boolean;
  channels?: string[];
  users?: string[];
  excludeUsers?: string[];
  messageTypes?: string[];
  badges?: string[];
  from?: Dayjs | null;
  to?: Dayjs | null;
  minLength?: number;
  maxLength?: number;
  firstMessagesOnly?: boolean;
  subscribersOnly?: boolean;
  vipsOnly?: boolean;
  moderatorsOnly?: boolean;
  hasEmotes?: boolean;
  searchDisplayNames?: boolean;
}

export function GlobalSearch() {
  const [searchText, setSearchText] = useState("");
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [advancedSearchParams, setAdvancedSearchParams] = useState<AdvancedSearchParams>({});
  const [isAdvancedSearchActive, setIsAdvancedSearchActive] = useState(false);
  const [useAdvancedMode, setUseAdvancedMode] = useState(false);
  const search = useRef<HTMLInputElement>(null);

  // Convert AdvancedSearchParams to the format expected by useGlobalSearch
  const convertToGlobalSearchParams = (params: AdvancedSearchParams) => ({
    q: params.q,
    regex: params.regex,
    caseSensitive: params.caseSensitive,
    channels: params.channels,
    users: params.users,
    excludeUsers: params.excludeUsers,
    messageTypes: params.messageTypes,
    badges: params.badges,
    from: params.from?.toDate() || null,
    to: params.to?.toDate() || null,
    minLength: params.minLength,
    maxLength: params.maxLength,
    firstMessagesOnly: params.firstMessagesOnly,
    subscribersOnly: params.subscribersOnly,
    vipsOnly: params.vipsOnly,
    moderatorsOnly: params.moderatorsOnly,
    hasEmotes: params.hasEmotes,
    searchDisplayNames: params.searchDisplayNames,
  });

  const globalSearchParams = useAdvancedMode 
    ? convertToGlobalSearchParams(advancedSearchParams)
    : { q: searchText };

  const { data: searchResults, isLoading, error } = useGlobalSearch(
    globalSearchParams,
    (useAdvancedMode && !!advancedSearchParams.q) || (!useAdvancedMode && !!searchText)
  );

  const handleAdvancedSearch = (params: AdvancedSearchParams) => {
    setAdvancedSearchParams(params);
    setIsAdvancedSearchActive(Object.keys(params).length > 0);
    setUseAdvancedMode(true);
  };

  const handleClearAdvancedSearch = () => {
    setAdvancedSearchParams({});
    setIsAdvancedSearchActive(false);
    setUseAdvancedMode(false);
  };

  const handleSimpleSearch = () => {
    setUseAdvancedMode(false);
    setAdvancedSearchParams({});
    setIsAdvancedSearchActive(false);
  };

  useEffect(() => {
    if (search.current) {
      search.current.focus();
    }
  }, []);

  return (
    <GlobalSearchContainer>
      <div className="search-header">
        <Public color="primary" fontSize="large" />
        <Typography variant="h4" component="h1">
          Global Search
        </Typography>
      </div>

      <Paper className="info-panel" elevation={1}>
        <Typography variant="body1" gutterBottom>
          Search across all logged channels and users. Use the advanced search for more precise filtering.
        </Typography>
        <FormControlLabel
          control={
            <Switch
              checked={useAdvancedMode}
              onChange={(e) => {
                setUseAdvancedMode(e.target.checked);
                if (!e.target.checked) {
                  handleClearAdvancedSearch();
                }
              }}
            />
          }
          label="Advanced Search Mode"
        />
      </Paper>

      <Box className="search-container">
        <TextField
          className="search"
          label={useAdvancedMode ? "Advanced Search Query" : "Search across all channels"}
          inputRef={search}
          value={useAdvancedMode ? (advancedSearchParams.q || "") : searchText}
          onChange={(e) => {
            if (useAdvancedMode) {
              setAdvancedSearchParams(prev => ({ ...prev, q: e.target.value }));
            } else {
              setSearchText(e.target.value);
            }
          }}
          size="small"
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
          }}
          placeholder={useAdvancedMode ? "Enter search terms..." : "Type to search all channels..."}
        />
        {useAdvancedMode && (
          <IconButton
            onClick={() => setShowAdvancedSearch(true)}
            title="Advanced Search Options"
            size="small"
            color={isAdvancedSearchActive ? "primary" : "default"}
          >
            <FilterList />
          </IconButton>
        )}
      </Box>

      {(searchResults || isLoading || error) && (
        <SearchResults
          results={searchResults}
          isLoading={isLoading}
          error={error}
          searchQuery={useAdvancedMode ? advancedSearchParams.q : searchText}
          onClearSearch={useAdvancedMode ? handleClearAdvancedSearch : () => setSearchText("")}
          isGlobalSearch={true}
        />
      )}

      <AdvancedSearch
        open={showAdvancedSearch}
        onClose={() => setShowAdvancedSearch(false)}
        onSearch={handleAdvancedSearch}
        initialParams={advancedSearchParams}
      />
    </GlobalSearchContainer>
  );
}
